import base64
import json
import sys
import time
from typing import Optional, List, Dict, Union, Any, Iterator

import click
import httpx
import llm
from pydantic import Field
from rich import print as rprint
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

# Type aliases for clarity
MessageDict = Dict[str, Union[str, List[Dict[str, Any]]]]
ToolCallDict = Dict[str, Any]

AVAILABLE_MODELS = [
    # Grok 4 models
    "x-ai/grok-4",
    "grok-4-heavy",
    # Grok 3 models
    "grok-3-latest",
    "grok-3-fast-latest",
    "grok-3-mini-latest",
    "grok-3-mini-fast-latest",
    # Grok 2 models
    "grok-2-latest",
    "grok-2-vision-latest",
]
DEFAULT_MODEL = "x-ai/grok-4"

# Model capabilities metadata
MODEL_INFO = {
    "x-ai/grok-4": {
        "context_window": 256000,
        "supports_vision": True,
        "supports_tools": True,
        "pricing_tier": "standard",
        "max_output_tokens": 8192,
    },
    "grok-4-heavy": {
        "context_window": 256000,
        "supports_vision": True,
        "supports_tools": True,
        "pricing_tier": "heavy",
        "max_output_tokens": 8192,
    },
    "grok-3-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
    "grok-3-fast-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
    "grok-3-mini-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "mini",
        "max_output_tokens": 4096,
    },
    "grok-3-mini-fast-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "mini",
        "max_output_tokens": 4096,
    },
    "grok-2-latest": {
        "context_window": 32768,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
    "grok-2-vision-latest": {
        "context_window": 32768,
        "supports_vision": True,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
}


@llm.hookimpl
def register_models(register) -> None:
    for model_id in AVAILABLE_MODELS:
        register(Grok(model_id))


class GrokError(Exception):
    """Base exception for Grok API errors"""

    def __init__(self, message, details=None):
        self.message = message
        self.details = details
        super().__init__(message)


class RateLimitError(GrokError):
    """Exception for rate limit errors"""

    pass


class QuotaExceededError(GrokError):
    """Exception for quota exceeded errors"""

    pass


class Grok(llm.KeyModel):
    can_stream = True
    needs_key = "grok"
    key_env_var = "XAI_API_KEY"
    MAX_RETRIES = 3
    BASE_DELAY = 1  # Base delay in seconds
    API_URL = "https://api.x.ai/v1/chat/completions"

    class Options(llm.Options):
        temperature: Optional[float] = Field(
            description=(
                "Determines the sampling temperature. Higher values like 0.8 increase randomness, "
                "while lower values like 0.2 make the output more focused and deterministic."
            ),
            ge=0,
            le=1,
            default=0.0,
        )
        max_completion_tokens: Optional[int] = Field(
            description="The maximum number of tokens to generate, including visible output tokens and reasoning tokens.",
            ge=0,
            default=None,
        )
        tools: Optional[List[Dict]] = Field(
            description="List of tool/function definitions in OpenAI format",
            default=None,
        )
        tool_choice: Optional[Union[str, Dict]] = Field(
            description="Controls which (if any) function is called. Can be 'auto', 'none', or a specific function",
            default=None,
        )
        response_format: Optional[Dict] = Field(
            description="Structured output format (e.g., {'type': 'json_object'})",
            default=None,
        )
        reasoning_effort: Optional[str] = Field(
            description="Level of reasoning effort for the model",
            default=None,
        )

    def __init__(self, model_id: str) -> None:
        self.model_id = model_id
    
    def _get_model_capability(self, capability: str) -> bool:
        """Check if current model supports a specific capability.
        
        Args:
            capability: The capability to check (e.g., 'supports_vision', 'supports_tools')
            
        Returns:
            bool: True if the model supports the capability, False otherwise
        """
        return MODEL_INFO.get(self.model_id, {}).get(capability, False)
    
    def _accumulate_tool_call(self, response: llm.Response, tool_call: Dict[str, Any]) -> None:
        """Helper to accumulate streaming tool call data.
        
        Args:
            response: The response object to accumulate tool calls into
            tool_call: The incremental tool call data from the stream
        """
        # Initialize tool_calls list if not exists
        if not hasattr(response, 'tool_calls'):
            response.tool_calls = []
        
        if tool_call.get("index") is not None:
            index = tool_call["index"]
            # Ensure list is large enough
            while len(response.tool_calls) <= index:
                response.tool_calls.append({})
            
            # Merge tool call data
            if "id" in tool_call:
                response.tool_calls[index]["id"] = tool_call["id"]
            if "type" in tool_call:
                response.tool_calls[index]["type"] = tool_call["type"]
            if "function" in tool_call:
                if "function" not in response.tool_calls[index]:
                    response.tool_calls[index]["function"] = {}
                if "name" in tool_call["function"]:
                    response.tool_calls[index]["function"]["name"] = tool_call["function"]["name"]
                if "arguments" in tool_call["function"]:
                    if "arguments" not in response.tool_calls[index]["function"]:
                        response.tool_calls[index]["function"]["arguments"] = ""
                    response.tool_calls[index]["function"]["arguments"] += tool_call["function"]["arguments"]

    def _validate_image_format(self, data: str) -> str:
        """Validate and format image data for multimodal API requests.
        
        Accepts image data in three formats:
        1. HTTP/HTTPS URLs - returned as-is
        2. Data URLs with base64 encoding - validated and returned
        3. Raw base64 strings - MIME type detected and formatted as data URL
        
        Args:
            data (str): The image data as URL, data URL, or base64 string
            
        Returns:
            str: Properly formatted image URL or data URL
            
        Raises:
            ValueError: If the image data is invalid or cannot be processed
        """
        if data.startswith(('http://', 'https://')):
            # URL - return as is
            return data
        elif data.startswith('data:'):
            # Data URL - validate format
            if ';base64,' in data:
                return data
            else:
                raise ValueError("Invalid data URL format - missing base64 indicator")
        else:
            # Assume raw base64 - try to detect MIME type
            try:
                # Validate and decode base64 once
                decoded = base64.b64decode(data, validate=True)
                
                # Try to detect image type from magic bytes
                header = decoded[:16]  # First few bytes for type detection
                mime_type = None
                
                if header.startswith(b'\xff\xd8\xff'):
                    mime_type = 'image/jpeg'
                elif header.startswith(b'\x89PNG'):
                    mime_type = 'image/png'
                elif header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
                    mime_type = 'image/gif'
                elif header.startswith(b'RIFF') and b'WEBP' in decoded[:20]:
                    mime_type = 'image/webp'
                else:
                    # Could not detect image type from magic bytes
                    raise ValueError(
                        "Unable to detect image type from base64 data. "
                        "Please provide a data URL with explicit MIME type or use a supported image format "
                        "(JPEG, PNG, GIF, or WebP)"
                    )
                
                return f"data:{mime_type};base64,{data}"
            except Exception as e:
                raise ValueError(f"Invalid base64 image data: {str(e)}")

    def _build_message_content(self, prompt: llm.Prompt) -> Union[str, List[Dict[str, Any]]]:
        """Build message content, handling multimodal inputs for vision-capable models.
        
        Constructs the content field of a message based on the model's capabilities
        and the presence of attachments. For vision-capable models with image
        attachments, returns an array format with text and image_url objects.
        For non-vision models or prompts without attachments, returns plain text.
        
        Args:
            prompt: The prompt object potentially containing text and attachments
            
        Returns:
            str or list: Plain text string for text-only content, or list of
                        content objects for multimodal content
        """
        # Check if model supports vision
        supports_vision = self._get_model_capability("supports_vision")
        
        # Check for attachments
        if hasattr(prompt, 'attachments') and prompt.attachments and supports_vision:
            content = [{"type": "text", "text": prompt.prompt}]
            
            for attachment in prompt.attachments:
                if attachment.type == "image":
                    try:
                        formatted_url = self._validate_image_format(attachment.data)
                        content.append({
                            "type": "image_url",
                            "image_url": {"url": formatted_url}
                        })
                    except ValueError as e:
                        # Log error but continue with other attachments
                        if "pytest" not in sys.modules:
                            console.print(f"[yellow]Warning: Skipping invalid image - {str(e)}[/yellow]")
            
            return content
        
        # Return plain text for non-multimodal or unsupported models
        return prompt.prompt

    def build_messages(self, prompt: llm.Prompt, conversation: Optional[llm.Conversation]) -> List[Dict[str, str]]:
        messages = []

        if prompt.system:
            messages.append({"role": "system", "content": prompt.system})

        if conversation:
            for prev_response in conversation.responses:
                if prev_response.prompt.system:
                    messages.append(
                        {"role": "system", "content": prev_response.prompt.system}
                    )
                messages.append(
                    {"role": "user", "content": self._build_message_content(prev_response.prompt)}
                )
                messages.append({"role": "assistant", "content": prev_response.text()})

        messages.append({"role": "user", "content": self._build_message_content(prompt)})
        return messages

    def _handle_rate_limit(self, response: httpx.Response, attempt: int) -> None:
        retry_after = response.headers.get("Retry-After")

        if retry_after:
            try:
                wait_time = int(retry_after)
                if attempt < self.MAX_RETRIES - 1:
                    with Progress(
                        SpinnerColumn(),
                        TextColumn("[progress.description]{task.description}"),
                        console=console,
                    ) as progress:
                        task = progress.add_task(
                            f"Rate limit hit. Waiting {wait_time}s as suggested by API...",
                            total=wait_time,
                        )
                        while not progress.finished:
                            time.sleep(0.1)
                            progress.update(task, advance=0.1)
                    return True
            except ValueError:
                pass

        if attempt < self.MAX_RETRIES - 1:
            delay = self.BASE_DELAY * (2**attempt)
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task(
                    f"Rate limit hit. Retrying in {delay}s...", total=delay
                )
                while not progress.finished:
                    time.sleep(0.1)
                    progress.update(task, advance=0.1)
            return True

        try:
            error_details = response.json()
            if "error" in error_details:
                error_message = error_details["error"].get("message", "")
                if (
                    "quota exceeded" in error_message.lower()
                    or "insufficient credits" in error_message.lower()
                ):
                    raise QuotaExceededError(
                        "API Quota Exceeded",
                        "Your x.ai API quota has been exceeded or you have insufficient credits.\n"
                        "Please visit https://x.ai to check your account status.",
                    )
        except:
            pass

        raise RateLimitError(
            "Rate Limit Exceeded",
            "You've hit the API rate limit. This could mean:\n"
            "1. Too many requests in a short time\n"
            "2. Your account has run out of credits\n\n"
            "Please visit https://x.ai to check your account status\n"
            "or wait a few minutes before trying again.",
        )

    def _make_request(self, client: httpx.Client, method: str, url: str, headers: Dict[str, str], json_data: Dict[str, Any], stream: bool = False) -> httpx.Response:
        """Execute HTTP request with retry logic for rate limiting.
        
        Args:
            client: httpx.Client or httpx.AsyncClient instance
            method: HTTP method (e.g., 'POST')
            url: Request URL
            headers: Request headers dict
            json_data: JSON payload for request body
            stream: Whether to stream the response
            
        Returns:
            httpx.Response or stream context manager
            
        Raises:
            RateLimitError: When rate limit is exceeded after all retries
            QuotaExceededError: When API quota is exceeded
            GrokError: For other API errors
        """
        for attempt in range(self.MAX_RETRIES):
            try:
                if stream:
                    return client.stream(
                        method, url, headers=headers, json=json_data, timeout=None
                    )
                else:
                    return client.request(
                        method, url, headers=headers, json=json_data, timeout=None
                    )
            except httpx.HTTPError as e:
                if (
                    hasattr(e, "response")
                    and e.response is not None
                    and e.response.status_code == 429
                ):
                    if self._handle_rate_limit(e.response, attempt):
                        continue
                raise

    def execute(self, prompt: llm.Prompt, stream: bool, response: llm.Response, conversation: Optional[llm.Conversation], key: Optional[str] = None) -> Iterator[str]:
        key = self.get_key(key)
        messages = self.build_messages(prompt, conversation)
        response._prompt_json = {"messages": messages}

        if not hasattr(prompt, "options") or not isinstance(
            prompt.options, self.Options
        ):
            options = self.Options()
        else:
            options = prompt.options

        body = {
            "model": self.model_id,
            "messages": messages,
            "stream": stream,
            "temperature": options.temperature,
        }

        # Get model info for capability checks
        supports_tools = self._get_model_capability("supports_tools")

        if options.max_completion_tokens is not None:
            # TODO: If max_completion_tokens runs out during reasoning, llm will crash when trying to log to db
            # This happens because Grok 4's automatic reasoning tokens can exceed the limit before generating
            # actual output tokens, causing the response to be empty. Consider implementing a minimum buffer
            # or warning when using low max_completion_tokens values with Grok 4 models.
            
            # Validate max_completion_tokens against model's limit
            model_info = MODEL_INFO.get(self.model_id, {})
            max_output_tokens = model_info.get("max_output_tokens")
            
            if max_output_tokens and options.max_completion_tokens > max_output_tokens:
                console.print(
                    f"[yellow]Warning: max_completion_tokens ({options.max_completion_tokens}) "
                    f"exceeds model's limit ({max_output_tokens}). Clamping to model limit.[/yellow]"
                )
                body["max_completion_tokens"] = max_output_tokens
            else:
                body["max_completion_tokens"] = options.max_completion_tokens
        
        # Add function calling parameters if model supports it
        if supports_tools:
            if options.tools is not None:
                body["tools"] = options.tools
                
            if options.tool_choice is not None:
                body["tool_choice"] = options.tool_choice
                
            if options.response_format is not None:
                body["response_format"] = options.response_format
                
        if options.reasoning_effort is not None:
            body["reasoning_effort"] = options.reasoning_effort

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {key}",
        }

        try:
            if stream:
                buffer = ""
                with httpx.Client() as client:
                    with self._make_request(
                        client,
                        "POST",
                        self.API_URL,
                        headers=headers,
                        json_data=body,
                        stream=True,
                    ) as r:
                        r.raise_for_status()
                        for chunk in r.iter_raw():
                            if chunk:
                                buffer += chunk.decode("utf-8")
                                while "\n\n" in buffer:
                                    message, buffer = buffer.split("\n\n", 1)
                                    if message.startswith("data: "):
                                        data = message[6:]
                                        if data == "[DONE]":
                                            break
                                        try:
                                            parsed = json.loads(data)
                                            if (
                                                "choices" in parsed
                                                and parsed["choices"]
                                            ):
                                                choice = parsed["choices"][0]
                                                delta = choice.get("delta", {})
                                                
                                                # Handle streaming content
                                                if "content" in delta:
                                                    content = delta["content"]
                                                    if content:
                                                        yield content
                                                
                                                # Handle streaming tool calls
                                                if "tool_calls" in delta:
                                                    # Accumulate tool calls from stream
                                                    for tool_call in delta["tool_calls"]:
                                                        self._accumulate_tool_call(response, tool_call)
                                        except json.JSONDecodeError as e:
                                            console.print(
                                                f"[yellow]Warning: Failed to parse JSON in streaming response: {str(e)}. "
                                                f"Skipping chunk: {data[:100]}{'...' if len(data) > 100 else ''}[/yellow]"
                                            )
                                            continue
            else:
                with httpx.Client() as client:
                    r = self._make_request(
                        client,
                        "POST",
                        self.API_URL,
                        headers=headers,
                        json_data=body,
                    )
                    r.raise_for_status()
                    response_data = r.json()
                    response.response_json = response_data
                    if "choices" in response_data and response_data["choices"]:
                        choice = response_data["choices"][0]
                        message = choice["message"]
                        
                        # Handle function/tool calls
                        if "tool_calls" in message and message["tool_calls"]:
                            # Store tool calls in response for potential handling
                            response.tool_calls = message["tool_calls"]
                            # For now, yield the content if any (might be None for pure tool calls)
                            if message.get("content"):
                                yield message["content"]
                        else:
                            # Regular content response
                            if message.get("content"):
                                yield message["content"]
        except httpx.HTTPError as e:
            if (
                hasattr(e, "response")
                and e.response is not None
                and e.response.status_code == 429
            ):
                try:
                    self._handle_rate_limit(e.response, self.MAX_RETRIES)
                except (RateLimitError, QuotaExceededError) as rate_error:
                    error_panel = Panel.fit(
                        f"[bold red]{rate_error.message}[/]\n\n[white]{rate_error.details}[/]",
                        title="❌ Error",
                        border_style="red",
                    )
                    if "pytest" in sys.modules:
                        raise rate_error
                    rprint(error_panel)
                    sys.exit(1)

            error_body = None
            if hasattr(e, "response") and e.response is not None:
                try:
                    if e.response.is_stream_consumed:
                        error_body = e.response.text
                    else:
                        error_body = e.response.read().decode("utf-8")
                except:
                    error_body = str(e)

            error_message = f"API Error: {str(e)}"
            if error_body:
                try:
                    error_json = json.loads(error_body)
                    if "error" in error_json and "message" in error_json["error"]:
                        error_message = error_json["error"]["message"]
                except:
                    pass

            error_panel = Panel.fit(
                f"[bold red]API Error[/]\n\n[white]{error_message}[/]",
                title="❌ Error",
                border_style="red",
            )
            if "pytest" in sys.modules:
                raise GrokError(error_message)
            rprint(error_panel)
            sys.exit(1)


@llm.hookimpl
def register_commands(cli) -> None:
    @cli.group()
    def grok():
        "Commands for the Grok model"

    @grok.command()
    def models():
        "Show available Grok models"
        click.echo("Available models:")
        for model in AVAILABLE_MODELS:
            if model == DEFAULT_MODEL:
                click.echo(f"  {model} (default)")
            else:
                click.echo(f"  {model}")
