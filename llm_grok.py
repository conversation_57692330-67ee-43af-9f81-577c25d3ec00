import base64
import json
import sys
import time
from typing import Optional, List, Dict, Union
import mimetypes

import click
import httpx
import llm
from pydantic import Field
from rich import print as rprint
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

AVAILABLE_MODELS = [
    # Grok 4 models
    "x-ai/grok-4",
    "grok-4-heavy",
    # Grok 3 models
    "grok-3-latest",
    "grok-3-fast-latest",
    "grok-3-mini-latest",
    "grok-3-mini-fast-latest",
    # Grok 2 models
    "grok-2-latest",
    "grok-2-vision-latest",
]
DEFAULT_MODEL = "x-ai/grok-4"

# Model capabilities metadata
MODEL_INFO = {
    "x-ai/grok-4": {
        "context_window": 256000,
        "supports_vision": True,
        "supports_tools": True,
        "pricing_tier": "standard",
        "max_output_tokens": 8192,
    },
    "grok-4-heavy": {
        "context_window": 256000,
        "supports_vision": True,
        "supports_tools": True,
        "pricing_tier": "heavy",
        "max_output_tokens": 8192,
    },
    "grok-3-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
    "grok-3-fast-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
    "grok-3-mini-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "mini",
        "max_output_tokens": 4096,
    },
    "grok-3-mini-fast-latest": {
        "context_window": 128000,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "mini",
        "max_output_tokens": 4096,
    },
    "grok-2-latest": {
        "context_window": 32768,
        "supports_vision": False,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
    "grok-2-vision-latest": {
        "context_window": 32768,
        "supports_vision": True,
        "supports_tools": False,
        "pricing_tier": "standard",
        "max_output_tokens": 4096,
    },
}


@llm.hookimpl
def register_models(register):
    for model_id in AVAILABLE_MODELS:
        register(Grok(model_id))


class GrokError(Exception):
    """Base exception for Grok API errors"""

    def __init__(self, message, details=None):
        self.message = message
        self.details = details
        super().__init__(message)


class RateLimitError(GrokError):
    """Exception for rate limit errors"""

    pass


class QuotaExceededError(GrokError):
    """Exception for quota exceeded errors"""

    pass


class Grok(llm.KeyModel):
    can_stream = True
    needs_key = "grok"
    key_env_var = "XAI_API_KEY"
    MAX_RETRIES = 3
    BASE_DELAY = 1  # Base delay in seconds
    API_URL = "https://api.x.ai/v1/chat/completions"

    class Options(llm.Options):
        temperature: Optional[float] = Field(
            description=(
                "Determines the sampling temperature. Higher values like 0.8 increase randomness, "
                "while lower values like 0.2 make the output more focused and deterministic."
            ),
            ge=0,
            le=1,
            default=0.0,
        )
        max_completion_tokens: Optional[int] = Field(
            description="The maximum number of tokens to generate, including visible output tokens and reasoning tokens.",
            ge=0,
            default=None,
        )
        tools: Optional[List[Dict]] = Field(
            description="List of tool/function definitions in OpenAI format",
            default=None,
        )
        tool_choice: Optional[Union[str, Dict]] = Field(
            description="Controls which (if any) function is called. Can be 'auto', 'none', or a specific function",
            default=None,
        )
        response_format: Optional[Dict] = Field(
            description="Structured output format (e.g., {'type': 'json_object'})",
            default=None,
        )
        reasoning_effort: Optional[str] = Field(
            description="Level of reasoning effort for the model",
            default=None,
        )

    def __init__(self, model_id):
        self.model_id = model_id

    def _validate_image_format(self, data):
        """Validate and format image data"""
        if data.startswith(('http://', 'https://')):
            # URL - return as is
            return data
        elif data.startswith('data:'):
            # Data URL - validate format
            if ';base64,' in data:
                return data
            else:
                raise ValueError("Invalid data URL format - missing base64 indicator")
        else:
            # Assume raw base64 - try to detect MIME type
            try:
                # Validate base64
                base64.b64decode(data, validate=True)
                
                # Try to detect image type from magic bytes
                decoded = base64.b64decode(data[:100])  # First few bytes
                mime_type = None
                
                if decoded.startswith(b'\xff\xd8\xff'):
                    mime_type = 'image/jpeg'
                elif decoded.startswith(b'\x89PNG'):
                    mime_type = 'image/png'
                elif decoded.startswith(b'GIF87a') or decoded.startswith(b'GIF89a'):
                    mime_type = 'image/gif'
                elif decoded.startswith(b'RIFF') and b'WEBP' in decoded[:20]:
                    mime_type = 'image/webp'
                else:
                    # Default to JPEG if we can't detect
                    mime_type = 'image/jpeg'
                
                return f"data:{mime_type};base64,{data}"
            except Exception as e:
                raise ValueError(f"Invalid base64 image data: {str(e)}")

    def _build_message_content(self, prompt):
        """Build message content, handling multimodal inputs"""
        # Check if model supports vision
        model_info = MODEL_INFO.get(self.model_id, {})
        supports_vision = model_info.get("supports_vision", False)
        
        # Check for attachments
        if hasattr(prompt, 'attachments') and prompt.attachments and supports_vision:
            content = [{"type": "text", "text": prompt.prompt}]
            
            for attachment in prompt.attachments:
                if attachment.type == "image":
                    try:
                        formatted_url = self._validate_image_format(attachment.data)
                        content.append({
                            "type": "image_url",
                            "image_url": {"url": formatted_url}
                        })
                    except ValueError as e:
                        # Log error but continue with other attachments
                        if "pytest" not in sys.modules:
                            console.print(f"[yellow]Warning: Skipping invalid image - {str(e)}[/yellow]")
            
            return content
        
        # Return plain text for non-multimodal or unsupported models
        return prompt.prompt

    def build_messages(self, prompt, conversation):
        messages = []

        if prompt.system:
            messages.append({"role": "system", "content": prompt.system})

        if conversation:
            for prev_response in conversation.responses:
                if prev_response.prompt.system:
                    messages.append(
                        {"role": "system", "content": prev_response.prompt.system}
                    )
                messages.append(
                    {"role": "user", "content": self._build_message_content(prev_response.prompt)}
                )
                messages.append({"role": "assistant", "content": prev_response.text()})

        messages.append({"role": "user", "content": self._build_message_content(prompt)})
        return messages

    def _handle_rate_limit(self, response, attempt):
        retry_after = response.headers.get("Retry-After")

        if retry_after:
            try:
                wait_time = int(retry_after)
                if attempt < self.MAX_RETRIES - 1:
                    with Progress(
                        SpinnerColumn(),
                        TextColumn("[progress.description]{task.description}"),
                        console=console,
                    ) as progress:
                        task = progress.add_task(
                            f"Rate limit hit. Waiting {wait_time}s as suggested by API...",
                            total=wait_time,
                        )
                        while not progress.finished:
                            time.sleep(1)
                            progress.update(task, advance=1)
                    return True
            except ValueError:
                pass

        if attempt < self.MAX_RETRIES - 1:
            delay = self.BASE_DELAY * (2**attempt)
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task(
                    f"Rate limit hit. Retrying in {delay}s...", total=delay
                )
                while not progress.finished:
                    time.sleep(1)
                    progress.update(task, advance=1)
            return True

        try:
            error_details = response.json()
            if "error" in error_details:
                error_message = error_details["error"].get("message", "")
                if (
                    "quota exceeded" in error_message.lower()
                    or "insufficient credits" in error_message.lower()
                ):
                    raise QuotaExceededError(
                        "API Quota Exceeded",
                        "Your x.ai API quota has been exceeded or you have insufficient credits.\n"
                        "Please visit https://x.ai to check your account status.",
                    )
        except:
            pass

        raise RateLimitError(
            "Rate Limit Exceeded",
            "You've hit the API rate limit. This could mean:\n"
            "1. Too many requests in a short time\n"
            "2. Your account has run out of credits\n\n"
            "Please visit https://x.ai to check your account status\n"
            "or wait a few minutes before trying again.",
        )

    def _make_request(self, client, method, url, headers, json_data, stream=False):
        for attempt in range(self.MAX_RETRIES):
            try:
                if stream:
                    return client.stream(
                        method, url, headers=headers, json=json_data, timeout=None
                    )
                else:
                    return client.request(
                        method, url, headers=headers, json=json_data, timeout=None
                    )
            except httpx.HTTPError as e:
                if (
                    hasattr(e, "response")
                    and e.response is not None
                    and e.response.status_code == 429
                ):
                    if self._handle_rate_limit(e.response, attempt):
                        continue
                raise

    def execute(self, prompt, stream, response, conversation, key=None):
        key = self.get_key(key)
        messages = self.build_messages(prompt, conversation)
        response._prompt_json = {"messages": messages}

        if not hasattr(prompt, "options") or not isinstance(
            prompt.options, self.Options
        ):
            options = self.Options()
        else:
            options = prompt.options

        body = {
            "model": self.model_id,
            "messages": messages,
            "stream": stream,
            "temperature": options.temperature,
        }

        if options.max_completion_tokens is not None:
            # TODO: If max_completion_tokens runs out during reasoning, llm will crash when trying to log to db
            # This happens because Grok 4's automatic reasoning tokens can exceed the limit before generating
            # actual output tokens, causing the response to be empty. Consider implementing a minimum buffer
            # or warning when using low max_completion_tokens values with Grok 4 models.
            body["max_completion_tokens"] = options.max_completion_tokens
        
        # Add function calling parameters if model supports it
        model_info = MODEL_INFO.get(self.model_id, {})
        if model_info.get("supports_tools", False):
            if options.tools is not None:
                body["tools"] = options.tools
                
            if options.tool_choice is not None:
                body["tool_choice"] = options.tool_choice
                
            if options.response_format is not None:
                body["response_format"] = options.response_format
                
        if options.reasoning_effort is not None:
            body["reasoning_effort"] = options.reasoning_effort

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {key}",
        }

        try:
            if stream:
                buffer = ""
                with httpx.Client() as client:
                    with self._make_request(
                        client,
                        "POST",
                        self.API_URL,
                        headers=headers,
                        json_data=body,
                        stream=True,
                    ) as r:
                        r.raise_for_status()
                        for chunk in r.iter_raw():
                            if chunk:
                                buffer += chunk.decode("utf-8")
                                while "\n\n" in buffer:
                                    message, buffer = buffer.split("\n\n", 1)
                                    if message.startswith("data: "):
                                        data = message[6:]
                                        if data == "[DONE]":
                                            break
                                        try:
                                            parsed = json.loads(data)
                                            if (
                                                "choices" in parsed
                                                and parsed["choices"]
                                            ):
                                                choice = parsed["choices"][0]
                                                delta = choice.get("delta", {})
                                                
                                                # Handle streaming content
                                                if "content" in delta:
                                                    content = delta["content"]
                                                    if content:
                                                        yield content
                                                
                                                # Handle streaming tool calls
                                                if "tool_calls" in delta:
                                                    # Initialize tool_calls list if not exists
                                                    if not hasattr(response, 'tool_calls'):
                                                        response.tool_calls = []
                                                    
                                                    # Accumulate tool calls from stream
                                                    for tool_call in delta["tool_calls"]:
                                                        if tool_call.get("index") is not None:
                                                            index = tool_call["index"]
                                                            # Ensure list is large enough
                                                            while len(response.tool_calls) <= index:
                                                                response.tool_calls.append({})
                                                            
                                                            # Merge tool call data
                                                            if "id" in tool_call:
                                                                response.tool_calls[index]["id"] = tool_call["id"]
                                                            if "type" in tool_call:
                                                                response.tool_calls[index]["type"] = tool_call["type"]
                                                            if "function" in tool_call:
                                                                if "function" not in response.tool_calls[index]:
                                                                    response.tool_calls[index]["function"] = {}
                                                                if "name" in tool_call["function"]:
                                                                    response.tool_calls[index]["function"]["name"] = tool_call["function"]["name"]
                                                                if "arguments" in tool_call["function"]:
                                                                    if "arguments" not in response.tool_calls[index]["function"]:
                                                                        response.tool_calls[index]["function"]["arguments"] = ""
                                                                    response.tool_calls[index]["function"]["arguments"] += tool_call["function"]["arguments"]
                                        except json.JSONDecodeError:
                                            continue
            else:
                with httpx.Client() as client:
                    r = self._make_request(
                        client,
                        "POST",
                        self.API_URL,
                        headers=headers,
                        json_data=body,
                    )
                    r.raise_for_status()
                    response_data = r.json()
                    response.response_json = response_data
                    if "choices" in response_data and response_data["choices"]:
                        choice = response_data["choices"][0]
                        message = choice["message"]
                        
                        # Handle function/tool calls
                        if "tool_calls" in message and message["tool_calls"]:
                            # Store tool calls in response for potential handling
                            response.tool_calls = message["tool_calls"]
                            # For now, yield the content if any (might be None for pure tool calls)
                            if message.get("content"):
                                yield message["content"]
                        else:
                            # Regular content response
                            yield message["content"]
        except httpx.HTTPError as e:
            if (
                hasattr(e, "response")
                and e.response is not None
                and e.response.status_code == 429
            ):
                try:
                    self._handle_rate_limit(e.response, self.MAX_RETRIES)
                except (RateLimitError, QuotaExceededError) as rate_error:
                    error_panel = Panel.fit(
                        f"[bold red]{rate_error.message}[/]\n\n[white]{rate_error.details}[/]",
                        title="❌ Error",
                        border_style="red",
                    )
                    if "pytest" in sys.modules:
                        raise rate_error
                    rprint(error_panel)
                    sys.exit(1)

            error_body = None
            if hasattr(e, "response") and e.response is not None:
                try:
                    if e.response.is_stream_consumed:
                        error_body = e.response.text
                    else:
                        error_body = e.response.read().decode("utf-8")
                except:
                    error_body = str(e)

            error_message = f"API Error: {str(e)}"
            if error_body:
                try:
                    error_json = json.loads(error_body)
                    if "error" in error_json and "message" in error_json["error"]:
                        error_message = error_json["error"]["message"]
                except:
                    pass

            error_panel = Panel.fit(
                f"[bold red]API Error[/]\n\n[white]{error_message}[/]",
                title="❌ Error",
                border_style="red",
            )
            if "pytest" in sys.modules:
                raise GrokError(error_message)
            rprint(error_panel)
            sys.exit(1)


@llm.hookimpl
def register_commands(cli):
    @cli.group()
    def grok():
        "Commands for the Grok model"

    @grok.command()
    def models():
        "Show available Grok models"
        click.echo("Available models:")
        for model in AVAILABLE_MODELS:
            if model == DEFAULT_MODEL:
                click.echo(f"  {model} (default)")
            else:
                click.echo(f"  {model}")
