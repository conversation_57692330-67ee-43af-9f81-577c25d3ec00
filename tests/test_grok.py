import base64
import json
import warnings

import httpx
import llm
import pytest
from pytest_httpx import HTT<PERSON><PERSON>Mock

from llm_grok import DEFAULT_MODEL, AVAILABLE_MODELS, MODEL_INFO, Grok, GrokError, register_models


@pytest.fixture(autouse=True)
def ignore_warnings():
    """Ignore known warnings."""
    # Filter out known deprecation warnings
    warnings.filterwarnings("ignore", message="Support for class-based `config` is deprecated")
    warnings.filterwarnings("ignore", message="datetime.datetime.utcnow() is deprecated")


@pytest.fixture
def model():
    return Grok(DEFAULT_MODEL)


@pytest.fixture(autouse=True)
def mock_env(monkeypatch):
    """Mock environment variables and API key for testing"""
    monkeypatch.setenv("XAI_API_KEY", "xai-test-key-mock")
    # Mock the get_key method to always return our test key
    def mock_get_key(self, key=None):
        return "xai-test-key-mock"
    monkeypatch.setattr(<PERSON><PERSON>, "get_key", mock_get_key)


@pytest.fixture
def mock_response():
    return {
        "id": "chatcmpl-123",
        "object": "chat.completion",
        "created": 1677652288,
        "choices": [
            {
                "index": 0,
                "message": {"role": "assistant", "content": "Test response"},
                "finish_reason": "stop",
            }
        ],
    }


def test_model_initialization(model):
    assert model.model_id == DEFAULT_MODEL
    assert model.can_stream == True
    assert model.needs_key == "grok"
    assert model.key_env_var == "XAI_API_KEY"


def test_grok_4_model_initialization():
    """Test initialization of Grok 4 models"""
    grok4 = Grok("x-ai/grok-4")
    assert grok4.model_id == "x-ai/grok-4"
    assert grok4.can_stream == True
    
    grok4_heavy = Grok("grok-4-heavy")
    assert grok4_heavy.model_id == "grok-4-heavy"
    assert grok4_heavy.can_stream == True


def test_model_info_registry():
    """Test that all models have metadata in MODEL_INFO"""
    for model_id in AVAILABLE_MODELS:
        assert model_id in MODEL_INFO
        info = MODEL_INFO[model_id]
        assert "context_window" in info
        assert "supports_vision" in info
        assert "supports_tools" in info
        assert "pricing_tier" in info
        assert "max_output_tokens" in info


def test_grok_4_model_info():
    """Test Grok 4 specific model capabilities"""
    # Test x-ai/grok-4
    grok4_info = MODEL_INFO["x-ai/grok-4"]
    assert grok4_info["context_window"] == 256000
    assert grok4_info["supports_vision"] == True
    assert grok4_info["supports_tools"] == True
    assert grok4_info["pricing_tier"] == "standard"
    assert grok4_info["max_output_tokens"] == 8192
    
    # Test grok-4-heavy
    heavy_info = MODEL_INFO["grok-4-heavy"]
    assert heavy_info["context_window"] == 256000
    assert heavy_info["supports_vision"] == True
    assert heavy_info["supports_tools"] == True
    assert heavy_info["pricing_tier"] == "heavy"
    assert heavy_info["max_output_tokens"] == 8192


def test_default_model_is_grok_4():
    """Test that default model is now Grok 4"""
    assert DEFAULT_MODEL == "x-ai/grok-4"


def test_build_messages_with_system_prompt(model):
    prompt = llm.Prompt(
        model=model, prompt="Test message", system="Custom system message"
    )
    messages = model.build_messages(prompt, None)

    assert len(messages) == 2
    assert messages[0]["role"] == "system"
    assert messages[0]["content"] == "Custom system message"
    assert messages[1]["role"] == "user"
    assert messages[1]["content"] == "Test message"


def test_build_messages_without_system_prompt(model):
    prompt = llm.Prompt(model=model, prompt="Test message")
    messages = model.build_messages(prompt, None)

    assert len(messages) == 1
    assert messages[0]["role"] == "user"
    assert messages[0]["content"] == "Test message"


def test_build_messages_with_conversation(model, httpx_mock: HTTPXMock, mock_env):
    # Mock the expected request content
    expected_request = {
        "model": DEFAULT_MODEL,
        "messages": [
            {"role": "user", "content": "Previous message"},
        ],
        "stream": False,
        "temperature": 0.0,
    }

    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json={
            "id": "chatcmpl-123",
            "choices": [
                {"message": {"role": "assistant", "content": "Previous response"}}
            ],
        },
        match_json=expected_request,
    )

    conversation = llm.Conversation(model=model)
    prev_prompt = llm.Prompt(model=model, prompt="Previous message")

    prev_response = llm.Response(model=model, prompt=prev_prompt, stream=False)
    prev_response._response_json = {
        "choices": [{"message": {"role": "assistant", "content": "Previous response"}}]
    }

    conversation.responses.append(prev_response)

    prompt = llm.Prompt(model=model, prompt="New message")
    messages = model.build_messages(prompt, conversation)

    assert len(messages) == 3
    assert messages[0]["role"] == "user"
    assert messages[0]["content"] == "Previous message"
    assert messages[1]["role"] == "assistant"
    assert messages[1]["content"] == "Previous response"
    assert messages[2]["role"] == "user"
    assert messages[2]["content"] == "New message"


def test_non_streaming_request(model, mock_response, httpx_mock: HTTPXMock, mock_env):
    expected_request = {
        "model": DEFAULT_MODEL,
        "messages": [
            {"role": "user", "content": "Test message"},
        ],
        "stream": False,
        "temperature": 0.0,
    }

    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        headers={"Content-Type": "application/json"},
        match_json=expected_request,
    )

    response = model.prompt("Test message", stream=False)
    result = response.text()
    assert result == "Test response"

    request = httpx_mock.get_requests()[0]
    assert request.headers["Authorization"] == "Bearer xai-test-key-mock"
    assert json.loads(request.content) == expected_request


def test_streaming_request(model, httpx_mock: HTTPXMock, mock_env):
    expected_request = {
        "model": DEFAULT_MODEL,
        "messages": [
            {"role": "user", "content": "Test message"},
        ],
        "stream": True,
        "temperature": 0.0,
    }

    def response_callback(request: httpx.Request) -> httpx.Response:
        assert request.headers["Authorization"] == "Bearer xai-test-key-mock"
        assert json.loads(request.content) == expected_request
        stream_content = [
            'data: {"id":"chatcmpl-123","choices":[{"delta":{"role":"assistant"}}]}\n\n',
            'data: {"id":"chatcmpl-123","choices":[{"delta":{"content":"Test"}}]}\n\n',
            'data: {"id":"chatcmpl-123","choices":[{"delta":{"content":" response"}}]}\n\n',
            "data: [DONE]\n\n",
        ]
        return httpx.Response(
            status_code=200,
            headers={"content-type": "text/event-stream"},
            content="".join(stream_content).encode(),
        )

    httpx_mock.add_callback(
        response_callback,
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        match_json=expected_request,
    )

    response = model.prompt("Test message", stream=True)
    chunks = list(response)
    assert "".join(chunks) == "Test response"


def test_temperature_option(model, mock_response, httpx_mock: HTTPXMock, mock_env):
    expected_request = {
        "model": DEFAULT_MODEL,
        "messages": [
            {"role": "user", "content": "Test message"},
        ],
        "stream": False,
        "temperature": 0.8,
    }

    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        headers={"Content-Type": "application/json"},
        match_json=expected_request,
    )

    # Create prompt and pass temperature directly
    response = model.prompt("Test message", stream=False, temperature=0.8)
    result = response.text()
    assert result == "Test response"

    request = httpx_mock.get_requests()[0]
    assert json.loads(request.content) == expected_request


def test_max_tokens_option(model, mock_response, httpx_mock: HTTPXMock, mock_env):
    expected_request = {
        "model": DEFAULT_MODEL,
        "messages": [
            {"role": "user", "content": "Test message"},
        ],
        "stream": False,
        "temperature": 0.0,
        "max_completion_tokens": 100,
    }

    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        headers={"Content-Type": "application/json"},
        match_json=expected_request,
    )

    # Create prompt and pass max_tokens directly
    response = model.prompt("Test message", stream=False, max_completion_tokens=100)
    result = response.text()
    assert result == "Test response"

    request = httpx_mock.get_requests()[0]
    assert json.loads(request.content) == expected_request


def test_api_error(model, httpx_mock: HTTPXMock, mock_env):
    expected_request = {
        "model": DEFAULT_MODEL,
        "messages": [
            {"role": "user", "content": "Test message"},
        ],
        "stream": False,
        "temperature": 0.0,
    }

    error_response = {
        "error": {
            "message": "Invalid request",
            "type": "invalid_request_error",
            "code": "invalid_api_key",
        }
    }

    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        status_code=400,
        json=error_response,
        headers={"Content-Type": "application/json"},
        match_json=expected_request,
    )

    with pytest.raises(GrokError) as exc_info:
        response = model.prompt("Test message", stream=False)
        response.text()  # Trigger the API call

    # The error message comes directly from the API response
    assert str(exc_info.value) == error_response["error"]["message"]


def test_stream_parsing_error(model, httpx_mock: HTTPXMock, mock_env):
    expected_request = {
        "model": DEFAULT_MODEL,
        "messages": [
            {"role": "user", "content": "Test message"},
        ],
        "stream": True,
        "temperature": 0.0,
    }

    def error_callback(request: httpx.Request) -> httpx.Response:
        assert request.headers["Authorization"] == "Bearer xai-test-key-mock"
        assert json.loads(request.content) == expected_request
        return httpx.Response(
            status_code=200,
            headers={"content-type": "text/event-stream"},
            content=b"data: {invalid json}\n\n",
        )

    httpx_mock.add_callback(
        error_callback,
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        match_json=expected_request,
    )

    response = model.prompt("Test message", stream=True)
    chunks = list(response)
    assert chunks == []


def test_grok_4_models_registered():
    """Test that Grok 4 models are registered in LLM"""
    # This test verifies models are available for registration
    models_to_register = []
    
    def mock_register(model):
        models_to_register.append(model)
    
    register_models(mock_register)
    
    model_ids = [model.model_id for model in models_to_register]
    assert "x-ai/grok-4" in model_ids
    assert "grok-4-heavy" in model_ids


def test_multimodal_message_building_with_url():
    """Test building multimodal messages with image URLs"""
    grok4 = Grok("x-ai/grok-4")
    
    # Create a mock prompt with image attachment
    class MockAttachment:
        def __init__(self, type, data):
            self.type = type
            self.data = data
    
    class MockPrompt:
        def __init__(self, prompt, attachments=None, system=None):
            self.prompt = prompt
            self.attachments = attachments or []
            self.system = system
    
    # Test with image URL
    prompt = MockPrompt(
        "What's in this image?",
        [MockAttachment("image", "https://example.com/image.jpg")]
    )
    
    messages = grok4.build_messages(prompt, None)
    
    assert len(messages) == 1
    assert messages[0]["role"] == "user"
    assert isinstance(messages[0]["content"], list)
    assert len(messages[0]["content"]) == 2
    assert messages[0]["content"][0] == {"type": "text", "text": "What's in this image?"}
    assert messages[0]["content"][1] == {
        "type": "image_url",
        "image_url": {"url": "https://example.com/image.jpg"}
    }


def test_multimodal_message_building_with_base64():
    """Test building multimodal messages with base64 images"""
    grok4 = Grok("x-ai/grok-4")
    
    class MockAttachment:
        def __init__(self, type, data):
            self.type = type
            self.data = data
    
    class MockPrompt:
        def __init__(self, prompt, attachments=None, system=None):
            self.prompt = prompt
            self.attachments = attachments or []
            self.system = system
    
    # Test with base64 image (minimal valid JPEG header)
    base64_image = "/9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAEBAQ=="
    prompt = MockPrompt(
        "Analyze this image",
        [MockAttachment("image", base64_image)]
    )
    
    messages = grok4.build_messages(prompt, None)
    
    assert len(messages) == 1
    assert messages[0]["role"] == "user"
    assert isinstance(messages[0]["content"], list)
    assert len(messages[0]["content"]) == 2
    assert messages[0]["content"][0] == {"type": "text", "text": "Analyze this image"}
    assert messages[0]["content"][1] == {
        "type": "image_url",
        "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
    }


def test_multimodal_message_building_with_data_url():
    """Test building multimodal messages with data URLs"""
    grok4 = Grok("x-ai/grok-4")
    
    class MockAttachment:
        def __init__(self, type, data):
            self.type = type
            self.data = data
    
    class MockPrompt:
        def __init__(self, prompt, attachments=None, system=None):
            self.prompt = prompt
            self.attachments = attachments or []
            self.system = system
    
    # Test with data URL
    data_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    prompt = MockPrompt(
        "What color is this pixel?",
        [MockAttachment("image", data_url)]
    )
    
    messages = grok4.build_messages(prompt, None)
    
    assert len(messages) == 1
    assert messages[0]["role"] == "user"
    assert isinstance(messages[0]["content"], list)
    assert messages[0]["content"][1] == {
        "type": "image_url",
        "image_url": {"url": data_url}
    }


def test_multimodal_only_for_vision_models():
    """Test that multimodal content is only used for vision-capable models"""
    # Test with non-vision model
    grok3 = Grok("grok-3-latest")  # This model doesn't support vision
    
    class MockAttachment:
        def __init__(self, type, data):
            self.type = type
            self.data = data
    
    class MockPrompt:
        def __init__(self, prompt, attachments=None, system=None):
            self.prompt = prompt
            self.attachments = attachments or []
            self.system = system
    
    prompt = MockPrompt(
        "What's in this image?",
        [MockAttachment("image", "https://example.com/image.jpg")]
    )
    
    messages = grok3.build_messages(prompt, None)
    
    # Should return plain text since model doesn't support vision
    assert len(messages) == 1
    assert messages[0]["role"] == "user"
    assert messages[0]["content"] == "What's in this image?"  # Plain string, not list


def test_image_validation():
    """Test image format validation"""
    grok4 = Grok("x-ai/grok-4")
    
    # Test valid formats
    assert grok4._validate_image_format("https://example.com/image.jpg") == "https://example.com/image.jpg"
    assert grok4._validate_image_format("http://example.com/image.jpg") == "http://example.com/image.jpg"
    
    # Test valid data URL
    data_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    assert grok4._validate_image_format(data_url) == data_url
    
    # Test base64 JPEG detection
    jpeg_base64 = "/9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAEBAQ=="
    result = grok4._validate_image_format(jpeg_base64)
    assert result.startswith("data:image/jpeg;base64,")
    
    # Test base64 PNG detection
    png_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    result = grok4._validate_image_format(png_base64)
    assert result.startswith("data:image/png;base64,")
    
    # Test invalid data URL
    try:
        grok4._validate_image_format("data:image/png,notbase64")
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "Invalid data URL format" in str(e)
    
    # Test invalid base64
    try:
        grok4._validate_image_format("not-valid-base64!")
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "Invalid base64 image data" in str(e)
    
    # Test unsupported image format (base64 data that doesn't match known magic bytes)
    # This is valid base64 but not a recognized image format
    unknown_format_base64 = base64.b64encode(b"UNKNOWN_FORMAT_DATA").decode()
    try:
        grok4._validate_image_format(unknown_format_base64)
        assert False, "Should have raised ValueError for unknown image format"
    except ValueError as e:
        assert "Unable to detect image type" in str(e)
        assert "supported image format" in str(e)


def test_function_calling_options():
    """Test that function calling options are properly set"""
    grok4 = Grok("x-ai/grok-4")
    
    # Test tools option
    tools = [{
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get the weather for a location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {"type": "string"}
                },
                "required": ["location"]
            }
        }
    }]
    
    options = grok4.Options(
        tools=tools,
        tool_choice="auto",
        response_format={"type": "json_object"},
        reasoning_effort="medium"
    )
    
    assert options.tools == tools
    assert options.tool_choice == "auto"
    assert options.response_format == {"type": "json_object"}
    assert options.reasoning_effort == "medium"


def test_function_calling_in_request_body(httpx_mock: HTTPXMock, mock_env):
    """Test that function calling parameters are included in API request for supported models"""
    grok4 = Grok("x-ai/grok-4")
    
    tools = [{
        "type": "function",
        "function": {
            "name": "calculate",
            "description": "Perform calculation",
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {"type": "string"}
                }
            }
        }
    }]
    
    # Create a mock prompt and response
    class MockPrompt:
        def __init__(self, prompt, options=None):
            self.prompt = prompt
            self.options = options
            self.system = None
            self.attachments = []
    
    class MockResponse:
        def __init__(self):
            self.response_json = None
            self.tool_calls = None
            self._prompt_json = None
        
        def text(self):
            if self.response_json and "choices" in self.response_json:
                choice = self.response_json["choices"][0]
                return choice.get("message", {}).get("content", "")
            return ""
    
    # Create prompt with function calling options
    prompt = MockPrompt("Calculate 2+2")
    prompt.options = grok4.Options(
        tools=tools,
        tool_choice="auto"
    )
    
    response = MockResponse()
    
    # Expected request should include tools
    expected_request = {
        "model": "x-ai/grok-4",
        "messages": [
            {"role": "user", "content": "Calculate 2+2"}
        ],
        "stream": False,
        "temperature": 0.0,
        "tools": tools,
        "tool_choice": "auto"
    }
    
    # Mock response with tool call
    mock_response = {
        "id": "chatcmpl-123",
        "choices": [{
            "message": {
                "role": "assistant",
                "content": None,
                "tool_calls": [{
                    "id": "call_123",
                    "type": "function",
                    "function": {
                        "name": "calculate",
                        "arguments": '{"expression": "2+2"}'
                    }
                }]
            }
        }]
    }
    
    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        match_json=expected_request
    )
    
    # Call execute method directly
    result = list(grok4.execute(prompt, stream=False, response=response, conversation=None))
    
    # Check that tool_calls are captured
    assert hasattr(response, 'tool_calls')
    assert len(response.tool_calls) == 1
    assert response.tool_calls[0]["function"]["name"] == "calculate"


def test_reasoning_effort_in_request_body(httpx_mock: HTTPXMock, mock_env):
    """Test that reasoning_effort is included in API request"""
    grok4 = Grok("x-ai/grok-4")
    
    # Mock classes
    class MockPrompt:
        def __init__(self, prompt, options=None):
            self.prompt = prompt
            self.options = options
            self.system = None
            self.attachments = []
    
    class MockResponse:
        def __init__(self):
            self.response_json = None
            self._prompt_json = None
    
    # Create prompt with reasoning_effort option
    prompt = MockPrompt("Solve this complex problem")
    prompt.options = grok4.Options(reasoning_effort="high")
    
    response = MockResponse()
    
    # Expected request should include reasoning_effort
    expected_request = {
        "model": "x-ai/grok-4",
        "messages": [
            {"role": "user", "content": "Solve this complex problem"}
        ],
        "stream": False,
        "temperature": 0.0,
        "reasoning_effort": "high"
    }
    
    mock_response = {
        "id": "chatcmpl-123",
        "choices": [{
            "message": {
                "role": "assistant",
                "content": "Solution to the problem"
            }
        }]
    }
    
    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        match_json=expected_request
    )
    
    # Execute request
    result = list(grok4.execute(prompt, stream=False, response=response, conversation=None))
    assert result == ["Solution to the problem"]


def test_response_format_in_request_body(httpx_mock: HTTPXMock, mock_env):
    """Test that response_format is included in API request for tool-supported models"""
    grok4 = Grok("x-ai/grok-4")
    
    # Mock classes
    class MockPrompt:
        def __init__(self, prompt, options=None):
            self.prompt = prompt
            self.options = options
            self.system = None
            self.attachments = []
    
    class MockResponse:
        def __init__(self):
            self.response_json = None
            self._prompt_json = None
    
    # Create prompt with response_format option
    prompt = MockPrompt("Generate a JSON object")
    prompt.options = grok4.Options(
        response_format={"type": "json_object"}
    )
    
    response = MockResponse()
    
    # Expected request should include response_format (only for tool-supported models)
    expected_request = {
        "model": "x-ai/grok-4",
        "messages": [
            {"role": "user", "content": "Generate a JSON object"}
        ],
        "stream": False,
        "temperature": 0.0,
        "response_format": {"type": "json_object"}
    }
    
    mock_response = {
        "id": "chatcmpl-123",
        "choices": [{
            "message": {
                "role": "assistant",
                "content": '{"result": "success"}'
            }
        }]
    }
    
    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        match_json=expected_request
    )
    
    # Execute request
    result = list(grok4.execute(prompt, stream=False, response=response, conversation=None))
    assert result == ['{"result": "success"}']


def test_all_options_combined(httpx_mock: HTTPXMock, mock_env):
    """Test that all options work together properly"""
    grok4 = Grok("x-ai/grok-4")
    
    # Mock classes
    class MockPrompt:
        def __init__(self, prompt, options=None):
            self.prompt = prompt
            self.options = options
            self.system = None
            self.attachments = []
    
    class MockResponse:
        def __init__(self):
            self.response_json = None
            self._prompt_json = None
    
    # Create prompt with all options
    tools = [{
        "type": "function",
        "function": {
            "name": "test_func",
            "description": "Test function",
            "parameters": {"type": "object", "properties": {}}
        }
    }]
    
    prompt = MockPrompt("Test all options")
    prompt.options = grok4.Options(
        temperature=0.7,
        max_completion_tokens=1000,
        tools=tools,
        tool_choice="auto",
        response_format={"type": "json_object"},
        reasoning_effort="medium"
    )
    
    response = MockResponse()
    
    # Expected request should include all parameters
    expected_request = {
        "model": "x-ai/grok-4",
        "messages": [
            {"role": "user", "content": "Test all options"}
        ],
        "stream": False,
        "temperature": 0.7,
        "max_completion_tokens": 1000,
        "tools": tools,
        "tool_choice": "auto",
        "response_format": {"type": "json_object"},
        "reasoning_effort": "medium"
    }
    
    mock_response = {
        "id": "chatcmpl-123",
        "choices": [{
            "message": {
                "role": "assistant",
                "content": '{"status": "all options received"}'
            }
        }]
    }
    
    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        match_json=expected_request
    )
    
    # Execute request
    result = list(grok4.execute(prompt, stream=False, response=response, conversation=None))
    assert result == ['{"status": "all options received"}']


def test_function_calling_not_sent_for_unsupported_models(httpx_mock: HTTPXMock, mock_env):
    """Test that function calling parameters are NOT sent for models that don't support it"""
    grok3 = Grok("grok-3-latest")  # This model doesn't support tools
    
    tools = [{
        "type": "function",
        "function": {
            "name": "test_func",
            "description": "Test function"
        }
    }]
    
    # Create a mock prompt and response
    class MockPrompt:
        def __init__(self, prompt, options=None):
            self.prompt = prompt
            self.options = options
            self.system = None
            self.attachments = []
    
    class MockResponse:
        def __init__(self):
            self.response_json = None
            self._prompt_json = None
    
    # Create prompt with function calling options
    prompt = MockPrompt("Test message")
    prompt.options = grok3.Options(
        tools=tools,
        tool_choice="auto"
    )
    
    response = MockResponse()
    
    # Expected request should NOT include tools
    expected_request = {
        "model": "grok-3-latest",
        "messages": [
            {"role": "user", "content": "Test message"}
        ],
        "stream": False,
        "temperature": 0.0
        # Note: no tools or tool_choice
    }
    
    mock_response = {
        "id": "chatcmpl-123",
        "choices": [{
            "message": {
                "role": "assistant",
                "content": "Regular response"
            }
        }]
    }
    
    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        match_headers={"Authorization": "Bearer xai-test-key-mock"},
        json=mock_response,
        match_json=expected_request
    )
    
    # Call execute method directly
    result = list(grok3.execute(prompt, stream=False, response=response, conversation=None))
    assert result == ["Regular response"]


def test_streaming_tool_calls_accumulation(httpx_mock: HTTPXMock, mock_env):
    """Test that streaming tool calls are properly accumulated across chunks"""
    grok4 = Grok("x-ai/grok-4")
    
    # Mock classes
    class MockPrompt:
        def __init__(self, prompt):
            self.prompt = prompt
            self.system = None
            self.options = None
    
    class MockResponse:
        def __init__(self):
            self.response_json = None
            self._prompt_json = None
            self.tool_calls = []
    
    prompt = MockPrompt("What's the weather in Paris and London?")
    prompt.options = grok4.Options(
        tools=[{
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {"location": {"type": "string"}},
                    "required": ["location"]
                }
            }
        }],
        tool_choice="auto"
    )
    
    response = MockResponse()
    
    # Create chunked SSE response that simulates tool calls being streamed
    chunks = [
        # First tool call starts
        b'data: {"choices":[{"delta":{"tool_calls":[{"index":0,"id":"call_1","type":"function","function":{"name":"get_weather"}}]}}]}\n\n',
        # First tool call arguments chunk 1
        b'data: {"choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"{\\"loc"}}]}}]}\n\n',
        # First tool call arguments chunk 2
        b'data: {"choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"ation\\": \\"Paris\\"}"}}]}}]}\n\n',
        # Second tool call starts
        b'data: {"choices":[{"delta":{"tool_calls":[{"index":1,"id":"call_2","type":"function","function":{"name":"get_weather"}}]}}]}\n\n',
        # Second tool call arguments
        b'data: {"choices":[{"delta":{"tool_calls":[{"index":1,"function":{"arguments":"{\\"location\\": \\"London\\"}"}}]}}]}\n\n',
        # Done
        b'data: [DONE]\n\n'
    ]
    
    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        headers={"content-type": "text/event-stream"},
        content=b"".join(chunks)
    )
    
    # Execute streaming request
    result = list(grok4.execute(prompt, stream=True, response=response, conversation=None))
    
    # Verify tool calls were accumulated correctly
    assert len(response.tool_calls) == 2
    
    # Check first tool call
    assert response.tool_calls[0]["id"] == "call_1"
    assert response.tool_calls[0]["type"] == "function"
    assert response.tool_calls[0]["function"]["name"] == "get_weather"
    assert response.tool_calls[0]["function"]["arguments"] == '{"location": "Paris"}'
    
    # Check second tool call
    assert response.tool_calls[1]["id"] == "call_2"
    assert response.tool_calls[1]["type"] == "function"
    assert response.tool_calls[1]["function"]["name"] == "get_weather"
    assert response.tool_calls[1]["function"]["arguments"] == '{"location": "London"}'


def test_streaming_tool_calls_with_content(httpx_mock: HTTPXMock, mock_env):
    """Test streaming with both content and tool calls"""
    grok4 = Grok("x-ai/grok-4")
    
    class MockPrompt:
        def __init__(self, prompt):
            self.prompt = prompt
            self.system = None
            self.options = None
    
    class MockResponse:
        def __init__(self):
            self.response_json = None
            self._prompt_json = None
            self.tool_calls = []
    
    prompt = MockPrompt("Check the weather and explain")
    prompt.options = grok4.Options(tools=[{"type": "function", "function": {"name": "get_weather"}}])
    
    response = MockResponse()
    
    # Mix content and tool calls in stream
    chunks = [
        b'data: {"choices":[{"delta":{"content":"I\'ll check "}}]}\n\n',
        b'data: {"choices":[{"delta":{"content":"the weather "}}]}\n\n',
        b'data: {"choices":[{"delta":{"tool_calls":[{"index":0,"id":"call_1","type":"function","function":{"name":"get_weather","arguments":"{\\"location\\": \\"NYC\\"}"}}]}}]}\n\n',
        b'data: {"choices":[{"delta":{"content":"for you."}}]}\n\n',
        b'data: [DONE]\n\n'
    ]
    
    httpx_mock.add_response(
        method="POST",
        url="https://api.x.ai/v1/chat/completions",
        headers={"content-type": "text/event-stream"},
        content=b"".join(chunks)
    )
    
    # Execute and collect content
    result = list(grok4.execute(prompt, stream=True, response=response, conversation=None))
    
    # Check content was collected
    assert result == ["I'll check ", "the weather ", "for you."]
    
    # Check tool call was also collected
    assert len(response.tool_calls) == 1
    assert response.tool_calls[0]["function"]["name"] == "get_weather"
    assert response.tool_calls[0]["function"]["arguments"] == '{"location": "NYC"}'
