# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is `llm-grok`, a Python plugin for the [LLM](https://llm.datasette.io/) CLI tool that provides access to Grok models using the xAI API. The plugin is published to PyPI and enables users to interact with various Grok models through the command line.

**Current Version**: 1.0 (modernized for Grok 4 support)

**Key Features**:
- **Grok 4 Support**: Full support for xAI's latest Grok 4 models with 256k context window
- **Multimodal Capabilities**: Image analysis support for vision-enabled models
- **Function Calling**: Tool/function calling support with parallel execution
- **Advanced Reasoning**: Support for Grok 4's enhanced reasoning capabilities
- **Backwards Compatibility**: Maintains support for all Grok 2 and Grok 3 models
- **Rich Terminal UI**: Enhanced user experience with progress indicators and error handling

## Development Commands

### Setup and Dependencies

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install development dependencies
pip install -e '.[test]'
```

### Testing

```bash
# Run all tests
pytest

# Run tests with verbose output
pytest -v

# Run specific test file
pytest tests/test_grok.py

# Run specific test
pytest tests/test_grok.py::test_model_initialization
```

### Building and Publishing

```bash
# Build the package
python -m build

# Upload to PyPI (requires credentials)
python -m twine upload dist/*
```

## Architecture

### Core Components

The plugin follows the LLM plugin architecture and consists of:

1. **`llm_grok.py`** - Single module containing all functionality:
   - `Grok` class: Main model implementation extending `llm.KeyModel`
   - `GrokError`, `RateLimitError`, `QuotaExceededError`: Custom exception classes
   - Plugin hooks: `register_models()` and `register_commands()`
   - Rate limiting and retry logic with exponential backoff
   - Multimodal message building with image validation
   - Function calling support with streaming tool calls

2. **Model Configuration**:
   - Available models defined in `AVAILABLE_MODELS` list (includes Grok 4 models)
   - Default model: `x-ai/grok-4` (updated from grok-3-latest)
   - `MODEL_INFO` registry with detailed metadata for each model:
     - Context window sizes (up to 256k tokens for Grok 4)
     - Vision support capabilities
     - Function calling support flags
     - Pricing tiers and output token limits
   - Supports streaming and non-streaming responses

3. **Enhanced Options**:
   - `temperature` (0-1): Sampling temperature control
   - `max_completion_tokens`: Maximum tokens to generate
   - `tools`: Function/tool definitions for function calling
   - `tool_choice`: Control over function selection ("auto", "none", or specific function)
   - `response_format`: Structured output format (e.g., JSON mode)
   - `reasoning_effort`: Control over reasoning depth

4. **API Integration**:
   - Base URL: `https://api.x.ai/v1/chat/completions`
   - Uses OpenAI-compatible chat completions API
   - Authentication via Bearer token from `XAI_API_KEY` or stored key
   - Multimodal message format for image inputs
   - Function calling with parallel tool execution

### Key Design Patterns

1. **Rate Limit Handling**: Automatic retry with exponential backoff and visual progress indicators using Rich
2. **Error Handling**: Graceful error messages with helpful user guidance
3. **Streaming Support**: Parses SSE (Server-Sent Events) format for real-time responses
4. **Message Building**: Constructs proper message arrays including system prompts and conversation history
5. **Model Capability Detection**: Uses `MODEL_INFO` registry to enable/disable features per model
6. **Multimodal Content Handling**: Validates and formats images for vision-capable models only
7. **Function Call Management**: Handles both streaming and non-streaming tool calls with proper accumulation
8. **Image Validation**: Robust validation for URLs, base64 data, and data URLs with MIME type detection

## Grok 4 Features

### Multimodal Support

The plugin now supports image analysis for vision-capable models (Grok 4, Grok 4 Heavy, and Grok 2 Vision):

- **Image Formats**: Supports URLs, base64 encoded images, and data URLs
- **MIME Type Detection**: Automatically detects and formats JPEG, PNG, GIF, and WebP images
- **Model Filtering**: Only enables multimodal features for models with `supports_vision: True`
- **Error Handling**: Graceful handling of invalid images with user-friendly warnings

### Function Calling

Full support for OpenAI-compatible function calling:

- **Tool Definitions**: JSON Schema-based function definitions
- **Parallel Execution**: Support for multiple simultaneous function calls
- **Streaming Support**: Real-time tool call accumulation in streaming mode
- **Tool Choice Control**: Options for automatic, manual, or disabled tool selection
- **Model Filtering**: Only available for models with `supports_tools: True`

### Enhanced Context Window

- **256k Token Context**: Grok 4 models support up to 256,000 token context windows
- **Automatic Handling**: No special configuration needed for large contexts
- **Backwards Compatibility**: Older models maintain their original context limits

### Reasoning Capabilities

- **Reasoning Effort Control**: Optional `reasoning_effort` parameter for controlling reasoning depth
- **Automatic Reasoning**: Grok 4 models automatically engage reasoning when beneficial
- **Transparent Operation**: Reasoning tokens are handled transparently by the API

## Testing Strategy

Tests use `pytest` with `pytest-httpx` for mocking HTTP requests. All API calls are mocked to avoid requiring real API keys during testing. The expanded test suite covers:

**Core Functionality**:
- Model initialization and configuration for all model variants
- Message building with/without system prompts and conversations
- Streaming and non-streaming requests
- Options handling (temperature, max_completion_tokens)
- Error scenarios and API error parsing

**Grok 4 Features**:
- Model metadata registry validation
- Grok 4 model initialization and capabilities
- Default model verification (x-ai/grok-4)

**Multimodal Support**:
- Image URL handling and validation
- Base64 image processing with MIME type detection
- Data URL format validation
- Model capability filtering (vision vs non-vision models)
- Error handling for invalid image formats

**Function Calling**:
- Tool definition validation
- Function calling request formatting
- Tool response handling in streaming and non-streaming modes
- Model capability filtering (tools vs non-tools models)
- Parallel tool call accumulation

**Test Coverage**: 20+ test functions covering all major features with comprehensive edge case handling.

## Development Patterns and Examples

### Model Capability Checking

Always check model capabilities before enabling features:

```python
model_info = MODEL_INFO.get(self.model_id, {})
supports_vision = model_info.get("supports_vision", False)
supports_tools = model_info.get("supports_tools", False)

if supports_vision and hasattr(prompt, 'attachments'):
    # Enable multimodal processing

if supports_tools and options.tools:
    # Include function calling parameters
```

### Multimodal Message Building

For vision-capable models, messages with images use array format:

```python
# Text-only message (all models)
{"role": "user", "content": "Hello"}

# Multimodal message (vision models only)
{
    "role": "user",
    "content": [
        {"type": "text", "text": "What's in this image?"},
        {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
    ]
}
```

### Image Validation Pattern

Robust image handling with validation:

```python
try:
    formatted_url = self._validate_image_format(attachment.data)
    content.append({
        "type": "image_url",
        "image_url": {"url": formatted_url}
    })
except ValueError as e:
    # Log warning but continue processing
    console.print(f"[yellow]Warning: Skipping invalid image - {str(e)}[/yellow]")
```

### Function Calling Request Format

For tool-capable models, include function definitions:

```python
body = {
    "model": "x-ai/grok-4",
    "messages": messages,
    "tools": [{
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get weather for a location",
            "parameters": {
                "type": "object",
                "properties": {"location": {"type": "string"}},
                "required": ["location"]
            }
        }
    }],
    "tool_choice": "auto"
}
```

### Tool Response Handling

Handle both streaming and non-streaming tool calls:

```python
# Non-streaming
if "tool_calls" in message and message["tool_calls"]:
    response.tool_calls = message["tool_calls"]

# Streaming - accumulate tool calls
if "tool_calls" in delta:
    for tool_call in delta["tool_calls"]:
        # Merge streaming tool call data
        index = tool_call.get("index", 0)
        # ... accumulation logic
```

## Important Notes

- The plugin requires an xAI API key, obtained from x.ai
- Supports both environment variable (`XAI_API_KEY`) and LLM key storage
- Implements proper rate limiting to handle API quotas gracefully
- Uses Rich for enhanced terminal output and progress indicators
- **Default model changed**: Now defaults to `x-ai/grok-4` (breaking change from v1.0)
- **Multimodal support**: Image analysis available for Grok 4, Grok 4 Heavy, and Grok 2 Vision
- **Function calling**: Available for Grok 4 models with full OpenAI compatibility
- **Large context**: Grok 4 models support up to 256k token context windows
- **Backwards compatibility**: All existing Grok 2 and Grok 3 models remain supported